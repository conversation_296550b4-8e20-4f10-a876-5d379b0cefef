import { BrowserRouter, Routes, Route } from "react-router";
import { AuthContextProvider } from "./context/AuthContext";
import Wellcome from "./pages/Wellcome";
import Chats from "./pages/Chats";
import Login from "./pages/Login";
import Register from "./pages/Register";

export default function App() {
  return (
    <BrowserRouter>
      <AuthContextProvider>
        <Routes>
          <Route path="/" element={<Chats />} />
          <Route path="/login" element={<Login />} />
          <Route path="/register" element={<Register />} />
          <Route path="/wellcome" element={<Wellcome />} />
        </Routes>
      </AuthContextProvider>
    </BrowserRouter>
  );
}
