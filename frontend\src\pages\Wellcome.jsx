import React from "react";
import { Link } from "react-router";

export default function Wellcome() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex flex-col items-center justify-center p-4">
      <div className="text-center mb-8">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">
          Welcome to Chat App
        </h1>
        <p className="text-lg text-gray-600">
          Connect with friends and start chatting
        </p>
      </div>
      <div className="space-x-4">
        <Link
          to="/login"
          className="inline-block px-6 py-3 bg-blue-500 text-white font-semibold rounded-lg shadow-md hover:bg-blue-600 transition"
        >
          Login
        </Link>
        <Link
          to="/register"
          className="inline-block px-6 py-3 bg-purple-500 text-white font-semibold rounded-lg shadow-md hover:bg-purple-600 transition"
        >
          Register
        </Link>
      </div>
    </div>
  );
}
