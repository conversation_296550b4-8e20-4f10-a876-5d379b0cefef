import { createContext, useState } from "react";
import axios from "axios";

export const AuthContext = createContext();

export const AuthContextProvider = ({ children }) => {
  const BACKEND_URL = import.meta.env.VITE_BACKEND_URL;

  const [isAuthorized, setIsAuthorized] = useState(false);

  const register = async (userData) => {
    try {
      const { data } = await axios.post(`${BACKEND_URL}/register`, userData);
      setIsAuthorized(true);
      console.log(data.message);
    } catch (error) {
      console.log(error.message);
    }
  };

  const login = async (userData) => {
    try {
      const { data } = await axios.post(`${BACKEND_URL}/login`, userData);
      setIsAuthorized(true);
      console.log(data.message);
    } catch (error) {
      console.log(error.message);
    }
  };

  const logout = async () => {
    try {
      const { data } = await axios.post(`${BACKEND_URL}/logout`);
      setIsAuthorized(false);
      console.log(data.message);
    } catch (error) {
      console.log(error.message);
    }
  };

  return (
    <AuthContext.Provider value={{ register, login, logout, isAuthorized }}>
      {children}
    </AuthContext.Provider>
  );
};
